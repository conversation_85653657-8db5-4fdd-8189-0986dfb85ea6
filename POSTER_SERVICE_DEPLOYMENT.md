# 海报生成服务 (poster-service) Linux 部署文档

## 项目概述

案例海报生成服务是一个基于 Node.js 和 Puppeteer 的微服务，用于生成案例海报截图。该服务提供 RESTful API 接口，支持自定义存储路径和高清截图生成。

## 系统要求

- **操作系统**: Linux (Ubuntu 18.04+ / CentOS 7+ / Debian 9+)
- **Node.js**: 16.x 或更高版本
- **内存**: 至少 2GB RAM (推荐 4GB+)
- **磁盘空间**: 至少 5GB 可用空间
- **网络**: 需要访问外网下载 Chromium

## 环境准备

### 1. 安装 Node.js

**Ubuntu/Debian 系统:**

```bash
# 更新包管理器
sudo apt update

# 安装 Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

**CentOS/RHEL 系统:**

```bash
# 安装 Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version
```

### 2. 安装系统依赖

Puppeteer 需要一些系统库支持，安装必要的依赖：

**Ubuntu/Debian:**

```bash
sudo apt-get install -y \
    ca-certificates \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libc6 \
    libcairo2 \
    libcups2 \
    libdbus-1-3 \
    libexpat1 \
    libfontconfig1 \
    libgbm1 \
    libgcc1 \
    libglib2.0-0 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libstdc++6 \
    libx11-6 \
    libx11-xcb1 \
    libxcb1 \
    libxcomposite1 \
    libxcursor1 \
    libxdamage1 \
    libxext6 \
    libxfixes3 \
    libxi6 \
    libxrandr2 \
    libxrender1 \
    libxss1 \
    libxtst6 \
    lsb-release \
    wget \
    xdg-utils
```

**CentOS/RHEL:**

```bash
sudo yum install -y \
    alsa-lib \
    atk \
    cups-libs \
    gtk3 \
    ipa-gothic-fonts \
    libXcomposite \
    libXcursor \
    libXdamage \
    libXext \
    libXi \
    libXrandr \
    libXScrnSaver \
    libXtst \
    pango \
    xorg-x11-fonts-100dpi \
    xorg-x11-fonts-75dpi \
    xorg-x11-fonts-cyrillic \
    xorg-x11-fonts-misc \
    xorg-x11-fonts-Type1 \
    xorg-x11-utils
```

## 部署步骤

### 1. 创建部署目录

```bash
# 创建应用目录
sudo mkdir -p /opt/case-management/poster-service
sudo chown $USER:$USER /opt/case-management/poster-service

# 创建上传目录
sudo mkdir -p /www/wwwroot/case-management/uploadPath/poster
sudo chown $USER:$USER /www/wwwroot/case-management/uploadPath/poster
```

### 2. 部署应用代码

```bash
# 进入部署目录
cd /opt/case-management/poster-service

# 复制项目文件 (假设从本地上传或从 Git 拉取)
# 方式1: 从本地复制
# scp -r ./case-api/poster-service/* user@server:/opt/case-management/poster-service/

# 方式2: 从 Git 仓库拉取 (如果有的话)
# git clone <repository-url> .
# 或者只复制 poster-service 目录内容

# 确保文件权限正确
chmod +x start.sh
```

### 3. 安装项目依赖

```bash
# 进入项目目录
cd /opt/case-management/poster-service

# 安装依赖
npm install --production

# 如果网络较慢，可以使用国内镜像
# npm install --production --registry=https://registry.npmmirror.com
```

### 4. 配置环境变量

创建环境配置文件：

```bash
# 创建环境配置文件
cat > /opt/case-management/poster-service/.env << EOF
# 服务端口
PORT=3001

# 上传目录
UPLOAD_DIR=/www/wwwroot/case-management/uploadPath/poster

# Node.js 环境
NODE_ENV=production
EOF
```

### 5. 测试服务

```bash
# 手动启动测试
cd /opt/case-management/poster-service
./start.sh
```

如果看到以下输出，说明服务启动成功：

```
海报生成服务已启动，端口: 3001
上传目录: /www/wwwroot/case-management/uploadPath/poster
健康检查: http://localhost:3001/health
服务信息: http://localhost:3001/api/info
```

按 `Ctrl+C` 停止测试。

## 生产环境部署

### 1. 使用 PM2 进程管理

安装 PM2：

```bash
sudo npm install -g pm2
```

创建 PM2 配置文件：

```bash
cat > /opt/case-management/poster-service/ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'poster-service',
    script: 'server.js',
    cwd: '/opt/case-management/poster-service',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001,
      UPLOAD_DIR: '/www/wwwroot/case-management/uploadPath/poster'
    },
    error_file: '/var/log/poster-service/error.log',
    out_file: '/var/log/poster-service/out.log',
    log_file: '/var/log/poster-service/combined.log',
    time: true
  }]
};
EOF
```

创建日志目录：

```bash
sudo mkdir -p /var/log/poster-service
sudo chown $USER:$USER /var/log/poster-service
```

启动服务：

```bash
cd /opt/case-management/poster-service
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 2. 配置系统服务 (Systemd)

如果不使用 PM2，可以创建 systemd 服务：

```bash
sudo tee /etc/systemd/system/poster-service.service > /dev/null << EOF
[Unit]
Description=Case Poster Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/opt/case-management/poster-service
Environment=NODE_ENV=production
Environment=PORT=3001
Environment=UPLOAD_DIR=/www/wwwroot/case-management/uploadPath/poster
ExecStart=/usr/bin/node server.js
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=poster-service

[Install]
WantedBy=multi-user.target
EOF
```

启动并启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable poster-service
sudo systemctl start poster-service
sudo systemctl status poster-service
```

## 反向代理配置

### Nginx 配置

如果使用 Nginx 作为反向代理：

```bash
sudo tee /etc/nginx/sites-available/poster-service << EOF
server {
    listen 80;
    server_name your-domain.com;  # 替换为你的域名

    location /api/poster/ {
        proxy_pass http://localhost:3001/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    location /health {
        proxy_pass http://localhost:3001/health;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 静态文件服务 (海报图片)
    location /poster/ {
        alias /www/wwwroot/case-management/uploadPath/poster/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/poster-service /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 防火墙配置

```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 3001/tcp

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload
```

## 监控和维护

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:3001/health

# 检查服务信息
curl http://localhost:3001/api/info
```

### 2. 日志查看

**PM2 方式:**

```bash
pm2 logs poster-service
pm2 logs poster-service --lines 100
```

**Systemd 方式:**

```bash
sudo journalctl -u poster-service -f
sudo journalctl -u poster-service --since "1 hour ago"
```

### 3. 性能监控

```bash
# PM2 监控
pm2 monit

# 系统资源监控
htop
free -h
df -h
```

## 故障排除

### 常见问题

1. **Puppeteer 下载 Chromium 失败**

   ```bash
   # 手动下载 Chromium
   cd /opt/case-management/poster-service
   npm config set puppeteer_download_host=https://npm.taobao.org/mirrors
   npm install puppeteer
   ```

2. **权限问题**

   ```bash
   # 确保目录权限正确
   sudo chown -R $USER:$USER /opt/case-management/poster-service
   sudo chown -R $USER:$USER /www/wwwroot/case-management/uploadPath/poster
   ```

3. **内存不足**

   ```bash
   # 增加 swap 空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
   ```

4. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :3001
   # 或者
   sudo lsof -i :3001
   ```

## 安全建议

1. **限制网络访问**

   - 只允许必要的 IP 访问服务端口
   - 使用防火墙规则限制访问

2. **定期更新**

   ```bash
   # 更新系统包
   sudo apt update && sudo apt upgrade  # Ubuntu/Debian
   sudo yum update  # CentOS/RHEL

   # 更新 Node.js 依赖
   cd /opt/case-management/poster-service
   npm audit
   npm update
   ```

3. **备份重要数据**
   ```bash
   # 备份上传的海报文件
   tar -czf poster-backup-$(date +%Y%m%d).tar.gz /www/wwwroot/case-management/uploadPath/poster/
   ```

## API 使用示例

服务部署成功后，可以通过以下方式调用 API：

```bash
# 生成海报
curl -X POST http://localhost:3001/api/generate-poster \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://example.com/case/123",
    "caseId": "123",
    "width": 1200,
    "height": 1600
  }'

# 健康检查
curl http://localhost:3001/health

# 获取服务信息
curl http://localhost:3001/api/info
```

## 更新部署

当需要更新服务时：

```bash
# 1. 停止服务
pm2 stop poster-service
# 或者 sudo systemctl stop poster-service

# 2. 备份当前版本
cp -r /opt/case-management/poster-service /opt/case-management/poster-service.backup

# 3. 更新代码
# 复制新的代码文件...

# 4. 安装新依赖
cd /opt/case-management/poster-service
npm install --production

# 5. 重启服务
pm2 start poster-service
# 或者 sudo systemctl start poster-service
```

## 服务接口说明

### 1. 生成海报接口

**接口地址:** `POST /api/generate-poster`

**请求参数:**

- `url` (必需): 海报页面 URL
- `caseId` (必需): 案例 ID
- `width` (可选): 截图宽度，默认 1200
- `height` (可选): 截图高度，默认 1600
- `uploadPath` (可选): 自定义存储路径

**响应示例:**

```json
{
  "success": true,
  "message": "海报生成成功",
  "data": {
    "fileName": "poster_case_123_1640995200000.png",
    "filePath": "/www/wwwroot/case-management/uploadPath/poster/poster_case_123_1640995200000.png",
    "relativePath": "/poster/poster_case_123_1640995200000.png",
    "uploadDir": "/www/wwwroot/case-management/uploadPath/poster",
    "caseId": "123"
  }
}
```

### 2. 健康检查接口

**接口地址:** `GET /health`

**响应示例:**

```json
{
  "success": true,
  "message": "海报生成服务运行正常",
  "timestamp": "2023-12-31T12:00:00.000Z"
}
```

### 3. 服务信息接口

**接口地址:** `GET /api/info`

**响应示例:**

```json
{
  "name": "Case Poster Service",
  "version": "1.1.0",
  "description": "案例海报生成服务（支持自定义存储路径）",
  "features": [
    "支持自定义存储路径",
    "自动创建目录",
    "高清截图生成",
    "完整页面截取"
  ],
  "endpoints": {
    "POST /api/generate-poster": {
      "description": "生成海报",
      "parameters": {
        "url": "海报页面URL (必需)",
        "caseId": "案例ID (必需)",
        "width": "截图宽度 (可选，默认1200)",
        "height": "截图高度 (可选，默认1600)",
        "uploadPath": "自定义存储路径 (可选)"
      }
    }
  },
  "defaultUploadDir": "/www/wwwroot/case-management/uploadPath/poster"
}
```

---

## 注意事项

- 确保服务器有足够的内存运行 Chromium (建议至少 2GB)
- 定期清理生成的海报文件以节省磁盘空间
- 监控服务性能，必要时调整 PM2 配置
- 建议在生产环境中使用 HTTPS
- Puppeteer 首次运行时会自动下载 Chromium，请确保网络连接正常
- 如果遇到字体渲染问题，可能需要安装中文字体包

## 技术支持

如果在部署过程中遇到问题，请检查：

1. Node.js 版本是否符合要求
2. 系统依赖是否完整安装
3. 目录权限是否正确
4. 防火墙和网络配置是否正确
5. 服务日志中的错误信息

更多技术细节请参考项目源码中的注释和配置文件。
