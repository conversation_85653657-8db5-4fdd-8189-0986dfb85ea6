#!/bin/bash

# 海报生成服务 Linux 部署启动脚本
# 适用于 Ubuntu/Debian/CentOS 系统

set -e

echo "=========================================="
echo "    案例海报生成服务 Linux 部署脚本"
echo "=========================================="

# 配置变量
SERVICE_NAME="poster-service"
SERVICE_DIR="/opt/case-management/poster-service"
UPLOAD_DIR="/www/wwwroot/case-management/uploadPath/poster"
SERVICE_PORT=3001
NODE_VERSION="18"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到root用户，建议使用普通用户运行此脚本"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检测系统类型
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        log_error "无法检测操作系统类型"
        exit 1
    fi
    
    log_info "检测到系统: $OS $VER"
}

# 安装Node.js
install_nodejs() {
    log_info "检查Node.js安装状态..."
    
    if command -v node &> /dev/null; then
        NODE_VER=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ $NODE_VER -ge $NODE_VERSION ]]; then
            log_info "Node.js已安装，版本: $(node --version)"
            return 0
        else
            log_warn "Node.js版本过低，需要升级"
        fi
    fi
    
    log_info "开始安装Node.js $NODE_VERSION.x..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        # Ubuntu/Debian
        sudo apt update
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        # CentOS/RHEL
        curl -fsSL https://rpm.nodesource.com/setup_${NODE_VERSION}.x | sudo bash -
        sudo yum install -y nodejs
    else
        log_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    log_info "Node.js安装完成: $(node --version)"
}

# 安装系统依赖
install_system_deps() {
    log_info "安装Puppeteer系统依赖..."
    
    if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
        sudo apt-get install -y \
            ca-certificates fonts-liberation libappindicator3-1 libasound2 \
            libatk-bridge2.0-0 libatk1.0-0 libc6 libcairo2 libcups2 \
            libdbus-1-3 libexpat1 libfontconfig1 libgbm1 libgcc1 \
            libglib2.0-0 libgtk-3-0 libnspr4 libnss3 libpango-1.0-0 \
            libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 \
            libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 \
            libxi6 libxrandr2 libxrender1 libxss1 libxtst6 lsb-release \
            wget xdg-utils
    elif [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
        sudo yum install -y \
            alsa-lib atk cups-libs gtk3 ipa-gothic-fonts libXcomposite \
            libXcursor libXdamage libXext libXi libXrandr libXScrnSaver \
            libXtst pango xorg-x11-fonts-100dpi xorg-x11-fonts-75dpi \
            xorg-x11-fonts-cyrillic xorg-x11-fonts-misc xorg-x11-fonts-Type1 \
            xorg-x11-utils
    fi
    
    log_info "系统依赖安装完成"
}

# 创建目录
create_directories() {
    log_info "创建服务目录..."
    
    sudo mkdir -p "$SERVICE_DIR"
    sudo mkdir -p "$UPLOAD_DIR"
    sudo chown -R $USER:$USER "$SERVICE_DIR"
    sudo chown -R $USER:$USER "$UPLOAD_DIR"
    
    log_info "目录创建完成"
}

# 部署服务文件
deploy_service() {
    log_info "部署服务文件..."
    
    # 检查源文件是否存在
    if [[ ! -f "case-api/poster-service/package.json" ]]; then
        log_error "未找到源文件，请确保在项目根目录运行此脚本"
        exit 1
    fi
    
    # 复制文件
    cp -r case-api/poster-service/* "$SERVICE_DIR/"
    chmod +x "$SERVICE_DIR/start.sh"
    
    log_info "服务文件部署完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    cd "$SERVICE_DIR"
    
    # 使用国内镜像加速
    npm config set registry https://registry.npmmirror.com
    npm install --production
    
    log_info "依赖安装完成"
}

# 安装PM2
install_pm2() {
    log_info "安装PM2进程管理器..."
    
    if ! command -v pm2 &> /dev/null; then
        sudo npm install -g pm2
        log_info "PM2安装完成"
    else
        log_info "PM2已安装"
    fi
}

# 创建PM2配置
create_pm2_config() {
    log_info "创建PM2配置文件..."
    
    cat > "$SERVICE_DIR/ecosystem.config.js" << EOF
module.exports = {
  apps: [{
    name: '$SERVICE_NAME',
    script: 'server.js',
    cwd: '$SERVICE_DIR',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: $SERVICE_PORT,
      UPLOAD_DIR: '$UPLOAD_DIR'
    }
  }]
};
EOF
    
    log_info "PM2配置文件创建完成"
}

# 启动服务
start_service() {
    log_info "启动海报生成服务..."
    
    cd "$SERVICE_DIR"
    
    # 停止可能存在的服务
    pm2 delete $SERVICE_NAME 2>/dev/null || true
    
    # 启动服务
    pm2 start ecosystem.config.js
    pm2 save
    
    # 设置开机自启
    pm2 startup
    
    log_info "服务启动完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian
        sudo ufw allow $SERVICE_PORT/tcp
        log_info "UFW防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL
        sudo firewall-cmd --permanent --add-port=$SERVICE_PORT/tcp
        sudo firewall-cmd --reload
        log_info "Firewalld防火墙规则已添加"
    else
        log_warn "未检测到防火墙，请手动开放端口 $SERVICE_PORT"
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    sleep 5
    
    if curl -s http://localhost:$SERVICE_PORT/health > /dev/null; then
        log_info "✅ 服务运行正常"
        echo
        echo "=========================================="
        echo "🎉 部署成功！"
        echo "=========================================="
        echo "服务地址: http://localhost:$SERVICE_PORT"
        echo "健康检查: http://localhost:$SERVICE_PORT/health"
        echo "服务信息: http://localhost:$SERVICE_PORT/api/info"
        echo "上传目录: $UPLOAD_DIR"
        echo
        echo "常用命令:"
        echo "  查看状态: pm2 status"
        echo "  查看日志: pm2 logs $SERVICE_NAME"
        echo "  重启服务: pm2 restart $SERVICE_NAME"
        echo "  停止服务: pm2 stop $SERVICE_NAME"
        echo "=========================================="
    else
        log_error "❌ 服务启动失败，请检查日志"
        pm2 logs $SERVICE_NAME --lines 20
        exit 1
    fi
}

# 主函数
main() {
    echo
    log_info "开始部署海报生成服务..."
    echo
    
    check_root
    detect_os
    install_nodejs
    install_system_deps
    create_directories
    deploy_service
    install_dependencies
    install_pm2
    create_pm2_config
    start_service
    configure_firewall
    health_check
}

# 执行主函数
main "$@"
